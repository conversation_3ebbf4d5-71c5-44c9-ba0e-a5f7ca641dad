// ignore_for_file: unused_import

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_demo/src/chat.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/tim_uikit_wide_modal_operation_key.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/avatar.dart';
import 'package:tencent_cloud_chat_demo/src/blackList.dart';
import 'package:tencent_cloud_chat_demo/src/group_list.dart';
import 'package:tencent_cloud_chat_demo/src/provider/local_setting.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/src/user_profile.dart';
import 'package:tencent_cloud_chat_uikit/ui/widgets/wide_popup.dart';
import 'newContact.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/life_cycle/friend_list_life_cycle.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/view_models/tui_friendship_view_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';

class Contact extends StatefulWidget {
  final ValueChanged<String>? onTapItem;

  const Contact({Key? key, this.onTapItem}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ContactState();
}

class _ContactState extends State<Contact> {
  int _friendListTotal = 0;
  final TUIFriendShipViewModel _model =
      serviceLocator<TUIFriendShipViewModel>();

  @override
  void initState() {
    super.initState();
    _initFriendList();
  }

  void _initFriendList() {
    _model.addListener(() {
      if (_model.friendList != null) {
        setState(() {
          _friendListTotal = _model.friendList!.length;
        });
        debugPrint('联系人列表总数更新: ${_model.friendList!.length}');
      }
    });
  }

  _topListItemTap(String id) {
    // 是否桌面端
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    switch (id) {
      case "newContact":
        if (isWideScreen) {
          TUIKitWidePopup.showPopupWindow(
            operationKey: TUIKitWideModalOperationKey.addNewContact,
            context: context,
            width: MediaQuery.of(context).size.width * 0.6,
            title: TIM_t("新的朋友"),
            height: MediaQuery.of(context).size.height * 0.6,
            child: (onClose) => const NewContact(),
          );
        } else {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const NewContact(),
              ));
        }
        break;
      case "groupList":
        if (isWideScreen) {
        } else {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => GroupList(),
              ));
        }
        break;
      case "blackList":
        if (isWideScreen) {
          TUIKitWidePopup.showPopupWindow(
            operationKey: TUIKitWideModalOperationKey.showBlockedUsers,
            context: context,
            width: MediaQuery.of(context).size.width * 0.6,
            title: TIM_t("黑名单"),
            height: MediaQuery.of(context).size.height * 0.6,
            child: (onClose) => const BlackList(),
          );
        } else {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const BlackList(),
              ));
        }
        break;
      case "nearbyList":
        if (isWideScreen) {
        } else {
          // 提示正在开发中
          ToastUtils.toast(TIM_t("正在开发中"));
        }
        break;
      default:
        break;
    }
  }

  String _getImagePathByID(String id) {
    final themeTypeSuffix = Provider.of<DefaultThemeData>(context)
        .currentThemeType
        .toString()
        .replaceFirst('ThemeType.', '');
    switch (id) {
      case "newContact":
        return "assets/newContact.png";
      case "groupList":
        return "assets/groupList.png";
      case "blackList":
        return "assets/blackList.png";
      case "customerService":
        return "assets/customerService.png";
      case "nearbyList":
        return "assets/nearbyList.png";
      default:
        return "";
    }
  }

  Widget? _topListBuilder(TopListItem item) {
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;
    final showName = item.name;

    if (item.id == "newContact") {
      return Column(children: [
        SizedBox(height: 16),
        Material(
            color: hexToColor("FFFFFF"),
            child: InkWell(
                onTap: () {
                  _topListItemTap(item.id);
                },
                child: Column(
                  children: [
                    Container(
                      padding:
                          const EdgeInsets.only(top: 0, left: 16, bottom: 0),
                      child: Row(
                        children: [
                          Container(
                            height: isWideScreen ? 30 : 46,
                            width: isWideScreen ? 30 : 46,
                            margin: const EdgeInsets.only(right: 12),
                            child: Avatar(
                              faceUrl: _getImagePathByID(item.id),
                              showName: showName,
                              isFromLocalAsset: true,
                            ),
                          ),
                          Expanded(
                            child: Container(
                              height: 66,
                              decoration: BoxDecoration(
                                  border: Border(
                                      bottom: BorderSide(
                                          color: hexToColor("DBDBDB")))),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    showName,
                                    style: TextStyle(
                                        color: hexToColor("111111"),
                                        fontSize: isWideScreen ? 14 : 14),
                                  ),
                                  Expanded(child: Container()),
                                  const TIMUIKitUnreadCount(),
                                  Container(
                                    color: hexToColor("000000"),
                                    margin: const EdgeInsets.only(right: 16),
                                  )
                                ],
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ],
                )))
      ]);
    }

    if (item.id == 'nearbyList') {
      return Column(
        children: [
          Material(
              color: hexToColor("FFFFFF"),
              child: InkWell(
                  onTap: () {
                    _topListItemTap(item.id);
                  },
                  child: Column(
                    children: [
                      Container(
                        padding:
                            const EdgeInsets.only(top: 0, left: 16, bottom: 0),
                        child: Row(
                          children: [
                            Container(
                              height: isWideScreen ? 30 : 46,
                              width: isWideScreen ? 30 : 46,
                              margin: const EdgeInsets.only(right: 12),
                              child: Avatar(
                                faceUrl: _getImagePathByID(item.id),
                                showName: showName,
                                isFromLocalAsset: true,
                              ),
                            ),
                            Expanded(
                              child: Container(
                                height: 66,
                                decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            color: hexToColor("DBDBDB")))),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      showName,
                                      style: TextStyle(
                                          color: hexToColor("111111"),
                                          fontSize: isWideScreen ? 14 : 14),
                                    ),
                                    Expanded(child: Container()),
                                    Container(
                                      color: hexToColor("000000"),
                                      margin: const EdgeInsets.only(right: 16),
                                    )
                                  ],
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ],
                  ))),
          Container(
            height: 48,
            color: hexToColor("FFFFFF"),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 42,
                  child: Container(
                    height: 0.5,
                    color: hexToColor("DBDBDB"),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Text(
                    TIM_t_para("{{option1}}位联系人", "${_friendListTotal}位联系人")(
                        option1: _friendListTotal),
                    style: TextStyle(
                      fontSize: 13,
                      color: hexToColor("999999"),
                    ),
                  ),
                ),
                SizedBox(
                  width: 42,
                  child: Container(
                    height: 0.5,
                    color: hexToColor("DBDBDB"),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Material(
        color: hexToColor("FFFFFF"),

        child: InkWell(
      onTap: () {
        _topListItemTap(item.id);
      },
      child: Container(
        padding: const EdgeInsets.only(top: 0, left: 16, bottom: 0),
        child: Row(
          children: [
            Container(
              height: isWideScreen ? 30 : 46,
              width: isWideScreen ? 30 : 46,
              margin: const EdgeInsets.only(right: 12),
              child: Avatar(
                faceUrl: _getImagePathByID(item.id),
                showName: showName,
                isFromLocalAsset: true,
              ),
            ),
            Expanded(
              child: Container(
                height: 66,
                decoration: BoxDecoration(
                    border: Border(
                        bottom: BorderSide(color: hexToColor("DBDBDB")))),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      showName,
                      style: TextStyle(
                          color: hexToColor("111111"),
                          fontSize: isWideScreen ? 14 : 14),
                    ),
                    Expanded(child: Container()),
                    Container(
                      color: hexToColor("000000"),
                      margin: const EdgeInsets.only(right: 16),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    ));
  }

  @override
  Widget build(BuildContext context) {
    final LocalSetting localSetting = Provider.of<LocalSetting>(context);
    final isWideScreen =
        TUIKitScreenUtils.getFormFactor(context) == DeviceType.Desktop;

    return Container(
        color: const Color(0xFFF9F9F9),
        child: Column(
          children: [
            Expanded(
                child: TIMUIKitContact(
              isShowOnlineStatus: localSetting.isShowOnlineStatus,
              lifeCycle: FriendListLifeCycle(
                friendListWillMount: (List<V2TimFriendInfo> list) async {
                  debugPrint('联系人列表总数: ${list.length}');
                  setState(() {
                    _friendListTotal = list.length;
                  });
                  return list;
                },
              ),
              topList: [
                TopListItem(
                    name: TIM_t("新的朋友"),
                    id: "newContact",
                    icon: Image.asset(_getImagePathByID("newContact")),
                    onTap: () {
                      _topListItemTap("newContact");
                    }),
                if (!isWideScreen)
                  TopListItem(
                      name: TIM_t("群聊"),
                      id: "groupList",
                      icon: Image.asset(_getImagePathByID("groupList")),
                      onTap: () {
                        _topListItemTap("groupList");
                      }),
                TopListItem(
                    name: TIM_t("黑名单"),
                    id: "blackList",
                    icon: Image.asset(_getImagePathByID("blackList")),
                    onTap: () {
                      _topListItemTap("blackList");
                    }),
                TopListItem(
                    name: TIM_t("附近的人"),
                    id: "nearbyList",
                    icon: Image.asset(_getImagePathByID("nearbyList")),
                    onTap: () {
                      _topListItemTap("nearbyList");
                    }),
              ],
              topListItemBuilder: _topListBuilder,
              onTapItem: (item) {
                if (widget.onTapItem != null) {
                  widget.onTapItem!(item.userID);
                } else {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => UserProfile(userID: item.userID),
                      ));
                }
              },
              emptyBuilder: (context) => Center(
                child: Text(TIM_t("无联系人")),
              ),
            ))
          ],
        ));
  }
}
